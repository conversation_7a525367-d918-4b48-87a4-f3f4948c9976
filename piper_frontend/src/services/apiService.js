const API_BASE = 'http://localhost:8000';

class ApiService {
  constructor() {
    this.baseUrl = API_BASE;
  }

  async checkApiStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok ? 'ready' : 'error';
    } catch (error) {
      console.error('API status check failed:', error);
      return 'error';
    }
  }

  async loadAvailableModels() {
    try {
      const response = await fetch(`${this.baseUrl}/models`);
      if (response.ok) {
        const data = await response.json();
        return data.models || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to load models:', error);
      return [];
    }
  }

  async loadVoiceInfo() {
    try {
      const response = await fetch(`${this.baseUrl}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('Failed to load voice info:', error);
      return null;
    }
  }

  async loadSpeakers() {
    try {
      const response = await fetch(`${this.baseUrl}/speakers`);
      if (response.ok) {
        const data = await response.json();
        return data.speakers || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to load speakers:', error);
      return [];
    }
  }

  async switchModel(modelId) {
    try {
      const response = await fetch(`${this.baseUrl}/models/${modelId}/load`, {
        method: 'POST'
      });
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      throw new Error('Failed to switch model');
    } catch (error) {
      console.error('Model switch failed:', error);
      throw error;
    }
  }

  async synthesizeSpeech(params) {
    try {
      const urlParams = new URLSearchParams({
        text: params.text,
        speaker_id: params.speakerId,
        speech_rate: params.speechRate,
        noise_scale: params.noiseScale,
        noise_w: params.noiseW,
        sentence_silence: params.sentenceSilence
      });

      const response = await fetch(`${this.baseUrl}/synthesize?${urlParams}`);

      if (response.ok) {
        const audioBlob = await response.blob();
        return URL.createObjectURL(audioBlob);
      }
      throw new Error('Failed to synthesize speech');
    } catch (error) {
      console.error('Synthesis error:', error);
      throw error;
    }
  }

  async generateAudio(params) {
    try {
      const requestBody = {
        text: params.text,
        speaker_id: params.speaker_id,
        speech_rate: params.speech_rate,
        noise_scale: params.noise_scale,
        noise_w: params.noise_w,
        sentence_silence: params.sentence_silence,
        use_voice_cloning: params.use_voice_cloning,
        reference_audio_base64: params.reference_audio_base64,
        cloning_model: params.cloning_model,
        cloning_strength: params.cloning_strength,
        language: params.language,
        use_timbre_transfer: params.use_timbre_transfer || false,
        timbre_strength: params.timbre_strength || 1.0,
        preserve_prosody: params.preserve_prosody !== false
      };

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        return URL.createObjectURL(audioBlob);
      }

      const errorText = await response.text();
      throw new Error(`Failed to generate audio: ${errorText}`);
    } catch (error) {
      console.error('Audio generation error:', error);
      throw error;
    }
  }

  async checkVoiceCloningAvailability() {
    try {
      const response = await fetch(`${this.baseUrl}/voice-cloning/available`);
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return {
        available_models: [],
        voice_cloning_enabled: false,
        message: "Voice cloning not available"
      };
    } catch (error) {
      console.error('Failed to check voice cloning availability:', error);
      return {
        available_models: [],
        voice_cloning_enabled: false,
        message: "Voice cloning not available"
      };
    }
  }
}

export default new ApiService();
